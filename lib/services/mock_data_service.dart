import 'dart:math';
import '../models/user_model.dart';
import '../models/video_model.dart';

class MockDataService {
  static final MockDataService _instance = MockDataService._internal();
  factory MockDataService() => _instance;
  MockDataService._internal();

  final Random _random = Random();

  // Mock video URLs (using sample videos from the internet)
  final List<String> _videoUrls = [
    'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerMeltdowns.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/SubaruOutbackOnStreetAndDirt.mp4',
  ];

  final List<String> _usernames = [
    'dance_queen', 'funny_guy', 'travel_lover', 'food_blogger', 'music_maker',
    'art_creator', 'fitness_guru', 'tech_reviewer', 'fashion_icon', 'pet_lover',
    'gamer_pro', 'book_worm', 'nature_photographer', 'comedy_king', 'singer_star'
  ];

  final List<String> _displayNames = [
    'Sarah Johnson', 'Mike Chen', 'Emma Wilson', 'David Kim', 'Lisa Garcia',
    'Alex Rodriguez', 'Maya Patel', 'James Brown', 'Sophie Taylor', 'Ryan Lee',
    'Zoe Martinez', 'Noah Davis', 'Ava Thompson', 'Ethan White', 'Mia Anderson'
  ];

  final List<String> _descriptions = [
    'Check out this amazing dance move! 💃 #dance #viral',
    'When you realize it\'s Monday tomorrow 😭 #mood #relatable',
    'Beautiful sunset from my trip to Bali 🌅 #travel #sunset',
    'Trying this new recipe and it\'s incredible! 🍕 #food #cooking',
    'New beat I\'ve been working on 🎵 #music #producer',
    'Digital art process timelapse ✨ #art #digital',
    'Morning workout routine 💪 #fitness #motivation',
    'Latest tech gadget review 📱 #tech #review',
    'OOTD for today\'s event 👗 #fashion #style',
    'My cat being adorable as always 🐱 #pets #cute',
    'Epic gaming moment! 🎮 #gaming #win',
    'Book recommendation of the week 📚 #books #reading',
    'Nature photography tips 📸 #photography #nature',
    'Stand-up comedy practice 😂 #comedy #funny',
    'Cover of my favorite song 🎤 #singing #cover'
  ];

  final List<String> _avatarUrls = [
    'https://i.pravatar.cc/150?img=1',
    'https://i.pravatar.cc/150?img=2',
    'https://i.pravatar.cc/150?img=3',
    'https://i.pravatar.cc/150?img=4',
    'https://i.pravatar.cc/150?img=5',
    'https://i.pravatar.cc/150?img=6',
    'https://i.pravatar.cc/150?img=7',
    'https://i.pravatar.cc/150?img=8',
    'https://i.pravatar.cc/150?img=9',
    'https://i.pravatar.cc/150?img=10',
    'https://i.pravatar.cc/150?img=11',
    'https://i.pravatar.cc/150?img=12',
    'https://i.pravatar.cc/150?img=13',
    'https://i.pravatar.cc/150?img=14',
    'https://i.pravatar.cc/150?img=15',
  ];

  final List<String> _commentTexts = [
    'This is amazing! 🔥',
    'Love this content!',
    'So cool! 😍',
    'Can\'t stop watching!',
    'You\'re so talented!',
    'This made my day 😊',
    'Incredible work!',
    'More content like this please!',
    'Absolutely stunning!',
    'You inspire me!',
    'This is so funny 😂',
    'Beautiful! ✨',
    'Mind blown 🤯',
    'Keep it up!',
    'Amazing as always!'
  ];

  UserModel _generateRandomUser() {
    final index = _random.nextInt(_usernames.length);
    return UserModel(
      id: 'user_${_random.nextInt(10000)}',
      username: _usernames[index],
      displayName: _displayNames[index],
      avatarUrl: _avatarUrls[index],
      followersCount: _random.nextInt(1000000) + 1000,
      followingCount: _random.nextInt(1000) + 50,
      isVerified: _random.nextBool(),
      isFollowing: _random.nextBool(),
    );
  }

  List<CommentModel> _generateRandomComments() {
    final commentCount = _random.nextInt(10) + 1;
    return List.generate(commentCount, (index) {
      return CommentModel(
        id: 'comment_${_random.nextInt(10000)}',
        user: _generateRandomUser(),
        content: _commentTexts[_random.nextInt(_commentTexts.length)],
        createdAt: DateTime.now().subtract(Duration(
          hours: _random.nextInt(24),
          minutes: _random.nextInt(60),
        )),
        likesCount: _random.nextInt(1000),
        isLiked: _random.nextBool(),
      );
    });
  }

  Future<List<VideoModel>> getVideos({int page = 0, int limit = 10}) async {
    // Simulate network delay
    await Future.delayed(Duration(milliseconds: 500 + _random.nextInt(1000)));

    return List.generate(limit, (index) {
      final videoIndex = (page * limit + index) % _videoUrls.length;
      final descriptionIndex = (page * limit + index) % _descriptions.length;
      
      return VideoModel(
        id: 'video_${page}_$index',
        videoUrl: _videoUrls[videoIndex],
        thumbnailUrl: 'https://picsum.photos/400/600?random=${page * limit + index}',
        user: _generateRandomUser(),
        description: _descriptions[descriptionIndex],
        likesCount: _random.nextInt(100000) + 1000,
        commentsCount: _random.nextInt(1000) + 10,
        sharesCount: _random.nextInt(10000) + 100,
        isLiked: _random.nextBool(),
        comments: _generateRandomComments(),
        duration: Duration(seconds: 15 + _random.nextInt(45)), // 15-60 seconds
        createdAt: DateTime.now().subtract(Duration(
          days: _random.nextInt(30),
          hours: _random.nextInt(24),
        )),
      );
    });
  }

  Future<VideoModel> likeVideo(String videoId, bool isLiked) async {
    // Simulate network delay
    await Future.delayed(Duration(milliseconds: 200 + _random.nextInt(300)));
    
    // In a real app, this would update the backend
    // For now, we'll just return a mock response
    throw UnimplementedError('Like functionality will be implemented with state management');
  }

  Future<CommentModel> addComment(String videoId, String content) async {
    // Simulate network delay
    await Future.delayed(Duration(milliseconds: 300 + _random.nextInt(500)));
    
    return CommentModel(
      id: 'comment_${_random.nextInt(10000)}',
      user: _generateRandomUser(), // In real app, this would be current user
      content: content,
      createdAt: DateTime.now(),
      likesCount: 0,
      isLiked: false,
    );
  }

  Future<UserModel> followUser(String userId, bool isFollowing) async {
    // Simulate network delay
    await Future.delayed(Duration(milliseconds: 200 + _random.nextInt(300)));
    
    // In a real app, this would update the backend
    throw UnimplementedError('Follow functionality will be implemented with state management');
  }
}
