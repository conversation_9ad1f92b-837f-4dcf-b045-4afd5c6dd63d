import 'package:flutter/material.dart';
// import 'package:cached_network_image/cached_network_image.dart';
import '../models/video_model.dart';
import '../utils/constants.dart';

class VideoActionsWidget extends StatelessWidget {
  final VideoModel video;
  final VoidCallback? onLike;
  final VoidCallback? onComment;
  final VoidCallback? onShare;
  final VoidCallback? onFollow;

  const VideoActionsWidget({
    Key? key,
    required this.video,
    this.onLike,
    this.onComment,
    this.onShare,
    this.onFollow,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60,
      padding: const EdgeInsets.only(bottom: 100),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // User Avatar with Follow Button
          _buildAvatarWithFollow(),
          const SizedBox(height: 24),
          
          // Like Button
          _buildActionButton(
            icon: video.isLiked ? Icons.favorite : Icons.favorite_border,
            count: _formatCount(video.likesCount),
            color: video.isLiked ? AppColors.primary : AppColors.white,
            onTap: onLike,
          ),
          const SizedBox(height: 24),
          
          // Comment Button
          _buildActionButton(
            icon: Icons.chat_bubble_outline,
            count: _formatCount(video.commentsCount),
            color: AppColors.white,
            onTap: onComment,
          ),
          const SizedBox(height: 24),
          
          // Share Button
          _buildActionButton(
            icon: Icons.share,
            count: _formatCount(video.sharesCount),
            color: AppColors.white,
            onTap: onShare,
          ),
          const SizedBox(height: 24),
          
          // Music/Sound Icon (decorative for now)
          _buildMusicIcon(),
        ],
      ),
    );
  }

  Widget _buildAvatarWithFollow() {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        // User Avatar
        GestureDetector(
          onTap: onFollow,
          child: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: AppColors.white,
                width: 2,
              ),
            ),
            child: ClipOval(
              child: Image.network(
                video.user.avatarUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  color: AppColors.grey,
                  child: const Icon(
                    Icons.person,
                    color: AppColors.white,
                    size: 24,
                  ),
                ),
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    color: AppColors.grey,
                    child: const Icon(
                      Icons.person,
                      color: AppColors.white,
                      size: 24,
                    ),
                  );
                },
              ),
            ),
          ),
        ),
        
        // Follow Button
        if (!video.user.isFollowing)
          Positioned(
            bottom: -6,
            child: GestureDetector(
              onTap: onFollow,
              child: Container(
                width: 20,
                height: 20,
                decoration: const BoxDecoration(
                  color: AppColors.primary,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.add,
                  color: AppColors.white,
                  size: 16,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String count,
    required Color color,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.black26,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 28,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            count,
            style: const TextStyle(
              color: AppColors.white,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMusicIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: Colors.black26,
        shape: BoxShape.circle,
      ),
      child: const Icon(
        Icons.music_note,
        color: AppColors.white,
        size: 24,
      ),
    );
  }

  String _formatCount(int count) {
    if (count < 1000) {
      return count.toString();
    } else if (count < 1000000) {
      double value = count / 1000.0;
      return value % 1 == 0 
          ? '${value.toInt()}K' 
          : '${value.toStringAsFixed(1)}K';
    } else {
      double value = count / 1000000.0;
      return value % 1 == 0 
          ? '${value.toInt()}M' 
          : '${value.toStringAsFixed(1)}M';
    }
  }
}
