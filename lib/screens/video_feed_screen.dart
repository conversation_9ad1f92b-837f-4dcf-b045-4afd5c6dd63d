import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// import 'package:provider/provider.dart';
// import 'package:share_plus/share_plus.dart';
import '../models/video_model.dart';
import '../services/mock_data_service.dart';
import '../widgets/video_player_widget.dart';
import '../widgets/video_actions_widget.dart';
import '../widgets/comment_panel.dart';
import '../utils/constants.dart';

class VideoFeedScreen extends StatefulWidget {
  const VideoFeedScreen({Key? key}) : super(key: key);

  @override
  State<VideoFeedScreen> createState() => _VideoFeedScreenState();
}

class _VideoFeedScreenState extends State<VideoFeedScreen> {
  final PageController _pageController = PageController();
  final MockDataService _mockDataService = MockDataService();
  
  List<VideoModel> _videos = [];
  int _currentIndex = 0;
  bool _isLoading = false;
  bool _hasError = false;
  String? _errorMessage;
  bool _showCommentPanel = false;

  @override
  void initState() {
    super.initState();
    _loadInitialVideos();
    
    // Set system UI overlay style for immersive experience
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.black,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialVideos() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final videos = await _mockDataService.getVideos(page: 0, limit: 10);
      setState(() {
        _videos = videos;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreVideos() async {
    if (_isLoading) return;

    try {
      final page = (_videos.length / 10).floor();
      final newVideos = await _mockDataService.getVideos(page: page, limit: 10);
      setState(() {
        _videos.addAll(newVideos);
      });
    } catch (e) {
      // Handle error silently for pagination
      debugPrint('Error loading more videos: $e');
    }
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });

    // Preload more videos when approaching the end
    if (index >= _videos.length - 3) {
      _loadMoreVideos();
    }
  }

  void _onVideoTap() {
    // Toggle play/pause or handle other tap actions
    // This will be handled by the video player widget
  }

  void _onLike() {
    setState(() {
      final video = _videos[_currentIndex];
      _videos[_currentIndex] = video.copyWith(
        isLiked: !video.isLiked,
        likesCount: video.isLiked 
            ? video.likesCount - 1 
            : video.likesCount + 1,
      );
    });
  }

  void _onComment() {
    setState(() {
      _showCommentPanel = true;
    });
  }

  void _onShare() {
    final video = _videos[_currentIndex];
    // Share.share(
    //   'Check out this amazing video by ${video.user.displayName}!\n${video.description}',
    //   subject: 'Amazing Video',
    // );
    
    // For now, just show a snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sharing: ${video.description}'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _onFollow() {
    setState(() {
      final video = _videos[_currentIndex];
      _videos[_currentIndex] = video.copyWith(
        user: video.user.copyWith(
          isFollowing: !video.user.isFollowing,
          followersCount: video.user.isFollowing
              ? video.user.followersCount - 1
              : video.user.followersCount + 1,
        ),
      );
    });
  }

  void _onAddComment(String comment) {
    // In a real app, this would call the API
    // For now, just close the comment panel
    setState(() {
      _showCommentPanel = false;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Comment added: $comment'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _onCloseCommentPanel() {
    setState(() {
      _showCommentPanel = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Stack(
        children: [
          // Main video feed
          _buildVideoFeed(),
          
          // Comment panel overlay
          if (_showCommentPanel)
            Positioned.fill(
              child: GestureDetector(
                onTap: _onCloseCommentPanel,
                child: Container(
                  color: Colors.black54,
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: GestureDetector(
                      onTap: () {}, // Prevent closing when tapping on panel
                      child: CommentPanel(
                        video: _videos.isNotEmpty ? _videos[_currentIndex] : _createEmptyVideo(),
                        onAddComment: _onAddComment,
                        onClose: _onCloseCommentPanel,
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildVideoFeed() {
    if (_isLoading && _videos.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primary,
        ),
      );
    }

    if (_hasError && _videos.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: AppColors.white,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'Failed to load videos',
              style: TextStyle(
                color: AppColors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            if (_errorMessage != null) ...[
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  _errorMessage!,
                  style: const TextStyle(
                    color: AppColors.grey,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadInitialVideos,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
              ),
              child: const Text(AppStrings.retry),
            ),
          ],
        ),
      );
    }

    if (_videos.isEmpty) {
      return const Center(
        child: Text(
          'No videos available',
          style: TextStyle(
            color: AppColors.white,
            fontSize: 16,
          ),
        ),
      );
    }

    return PageView.builder(
      controller: _pageController,
      scrollDirection: Axis.vertical,
      onPageChanged: _onPageChanged,
      itemCount: _videos.length,
      itemBuilder: (context, index) {
        final video = _videos[index];
        final isCurrentVideo = index == _currentIndex;
        
        return _buildVideoPage(video, isCurrentVideo);
      },
    );
  }

  Widget _buildVideoPage(VideoModel video, bool isCurrentVideo) {
    return Stack(
      children: [
        // Video player (full screen)
        VideoPlayerWidget(
          video: video,
          isPlaying: isCurrentVideo,
          onTap: _onVideoTap,
        ),
        
        // Video information overlay (bottom left)
        Positioned(
          left: 16,
          bottom: 100,
          right: 80,
          child: _buildVideoInfo(video),
        ),
        
        // Action buttons (right side)
        Positioned(
          right: 8,
          bottom: 0,
          child: VideoActionsWidget(
            video: video,
            onLike: _onLike,
            onComment: _onComment,
            onShare: _onShare,
            onFollow: _onFollow,
          ),
        ),
      ],
    );
  }

  Widget _buildVideoInfo(VideoModel video) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Username
        Text(
          '@${video.user.username}',
          style: const TextStyle(
            color: AppColors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        
        // Description
        Text(
          video.description,
          style: const TextStyle(
            color: AppColors.white,
            fontSize: 14,
          ),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  VideoModel _createEmptyVideo() {
    return VideoModel(
      id: '',
      videoUrl: '',
      thumbnailUrl: '',
      user: UserModel(
        id: '',
        username: '',
        displayName: '',
        avatarUrl: '',
        followersCount: 0,
        followingCount: 0,
      ),
      description: '',
      duration: Duration.zero,
      createdAt: DateTime.now(),
    );
  }
}
