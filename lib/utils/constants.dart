import 'package:flutter/material.dart';

class AppColors {
  static const Color primary = Color(0xFFFF0050);
  static const Color secondary = Color(0xFF25F4EE);
  static const Color background = Color(0xFF000000);
  static const Color surface = Color(0xFF161823);
  static const Color white = Color(0xFFFFFFFF);
  static const Color grey = Color(0xFF8A8A8A);
  static const Color darkGrey = Color(0xFF2F2F2F);
}

class AppConstants {
  static const double borderRadius = 12.0;
  static const double padding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  
  // Video player constants
  static const double videoAspectRatio = 9 / 16;
  static const int preloadVideoCount = 3;
  static const Duration videoTransitionDuration = Duration(milliseconds: 300);
  
  // Animation constants
  static const Duration defaultAnimationDuration = Duration(milliseconds: 200);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
}

class AppStrings {
  static const String appName = 'Douyin';
  static const String like = 'Like';
  static const String comment = 'Comment';
  static const String share = 'Share';
  static const String follow = 'Follow';
  static const String following = 'Following';
  static const String addComment = 'Add a comment...';
  static const String noComments = 'No comments yet';
  static const String errorLoadingVideo = 'Error loading video';
  static const String retry = 'Retry';
  static const String loading = 'Loading...';
}
