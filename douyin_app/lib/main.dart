import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'screens/video_feed_screen.dart';
import 'utils/constants.dart';

void main() {
  runApp(const DouyinApp());
}

class DouyinApp extends StatelessWidget {
  const DouyinApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppStrings.appName,
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primaryColor: AppColors.primary,
        scaffoldBackgroundColor: AppColors.background,
        colorScheme: ColorScheme.fromSeed(
          seedColor: AppColors.primary,
          brightness: Brightness.dark,
        ),
        useMaterial3: true,
      ),
      home: const VideoFeedScreen(),
    );
  }
}


